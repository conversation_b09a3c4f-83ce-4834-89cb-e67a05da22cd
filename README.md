# go-613

--- 

### 介绍

内网设备之间文件快速传输中间件。

---

### 需求
设备自维护网络可达ip表。从可达ip表中选择，向指定ip进行发送文件，传输时间<6ms。无需考虑文件内容。


---

### 实现

- 内网主机发现 (UDP广播 + ARP扫描 + 端口扫描)

  ![](fig/process.png)

- 文件传输 (调用Go gRPC)
![](fig/transfer_describe.png)

---

### 运行截图

**1**. 主机1启动发现，仅发现自己。
   ![](fig/host1_start_label.png)

**2**. 主机2启动发现。主机1和主机2可以相互发现。
   ![](fig/host2_start_label.png)

**3**. 主机1启动gRPC客户端，根据相互发现节点，选择向主机2发送图片成功。
![](fig/host1-grpc-send-to-host2_label.png)

![](internal/grpclient/process.png)

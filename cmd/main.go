package main

import (
	"FBgo/internal/discovery"
	"FBgo/internal/server"
	"log"
	"time"
)

func main() {
	go server.StartServer()

	discovery.UpdateIntranetIP()
	log.Println("Local ip:", discovery.Local_address)

	go discovery.StartUDPServer("50052") // UDP监听
	time.Sleep(88 * time.Millisecond)
	// tcp传输 50053
	go discovery.StartTCPServer(discovery.Local_address, "50054") // TCP监听
	go discovery.StartTCPServer("127.0.0.1", "50054")             // TCP监听
	time.Sleep(88 * time.Millisecond)

	log.Println("------------------------------------------------")

	go discovery.StartDiscover() // 启动发现定时器

	select {}
}

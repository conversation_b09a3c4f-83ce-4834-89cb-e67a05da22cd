package clients

import (
	"encoding/binary"
	"fmt"
	"github.com/sirupsen/logrus"
	"io"
	"net"
	"time"
)

const (
	defaultPort = "22" // 默认端口
)

// 日志记录器
var logger = logrus.New()

// SendMessage 向服务端发送消息并接收响应
func SendMessage(targetIP, targetPort string, message []byte) (string, error) {
	if targetPort == "" {
		targetPort = defaultPort
	}
	address := fmt.Sprintf("%s:%s", targetIP, targetPort)
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		logger.Errorf("Failed to connect to %s: %v", address, err)
		return "", fmt.Errorf("failed to connect to %s: %v", address, err)
	}
	defer func(conn net.Conn) {
		err := conn.Close()
		if err != nil {
			logger.Errorf("Failed to close connection: %v", err)
		}
	}(conn)
	messageLength := int32(len(message))
	if err := binary.Write(conn, binary.BigEndian, messageLength); err != nil {
		logger.Errorf("Failed to write message length: %v", err)
		return "", fmt.Errorf("failed to write message length: %v", err)
	}
	if _, err := conn.Write(message); err != nil {
		logger.Errorf("Failed to send message: %v", err)
		return "", fmt.Errorf("failed to send message: %v", err)
	}
	var responseLength int32
	if err := binary.Read(conn, binary.BigEndian, &responseLength); err != nil {
		logger.Errorf("Failed to read response length: %v", err)
		return "", fmt.Errorf("failed to read response length: %v", err)
	}
	responseBuffer := make([]byte, responseLength)
	if _, err := io.ReadFull(conn, responseBuffer); err != nil {
		logger.Errorf("Failed to read full response: %v", err)
		return "", fmt.Errorf("failed to read full response: %v", err)
	} else {
		return "recived the photo", nil
	}

}

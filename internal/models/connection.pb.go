// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.0
// source: connection.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PictureData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 图片名称
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 图片字节数据
}

func (x *PictureData) Reset() {
	*x = PictureData{}
	mi := &file_connection_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PictureData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PictureData) ProtoMessage() {}

func (x *PictureData) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PictureData.ProtoReflect.Descriptor instead.
func (*PictureData) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{0}
}

func (x *PictureData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PictureData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// 空的请求，表示无参数
type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_connection_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{1}
}

// 用于发送图片请求时的参数，可指定图片数量，默认全部
type PictureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PictureCount int32 `protobuf:"varint,1,opt,name=picture_count,json=pictureCount,proto3" json:"picture_count,omitempty"` // 要发送的图片数量，小于等于0表示发送全部图片
}

func (x *PictureRequest) Reset() {
	*x = PictureRequest{}
	mi := &file_connection_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PictureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PictureRequest) ProtoMessage() {}

func (x *PictureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PictureRequest.ProtoReflect.Descriptor instead.
func (*PictureRequest) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{2}
}

func (x *PictureRequest) GetPictureCount() int32 {
	if x != nil {
		return x.PictureCount
	}
	return 0
}

// 响应消息，包含图片数据（以字节流形式等）以及可能的相关响应信息
type LocalPictureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PictureData   []*PictureData `protobuf:"bytes,1,rep,name=picture_data,json=pictureData,proto3" json:"picture_data,omitempty"`       // 图片数据结构体列表，每张图片对应一个PictureData实例
	StatusMessage string         `protobuf:"bytes,2,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"` // 发送状态相关的消息，比如成功、失败原因等
}

func (x *LocalPictureResponse) Reset() {
	*x = LocalPictureResponse{}
	mi := &file_connection_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalPictureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalPictureResponse) ProtoMessage() {}

func (x *LocalPictureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalPictureResponse.ProtoReflect.Descriptor instead.
func (*LocalPictureResponse) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{3}
}

func (x *LocalPictureResponse) GetPictureData() []*PictureData {
	if x != nil {
		return x.PictureData
	}
	return nil
}

func (x *LocalPictureResponse) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

// 节点信息列表
type NodeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []string `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"` // 存储节点信息的字符串列表
}

func (x *NodeList) Reset() {
	*x = NodeList{}
	mi := &file_connection_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeList) ProtoMessage() {}

func (x *NodeList) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeList.ProtoReflect.Descriptor instead.
func (*NodeList) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{4}
}

func (x *NodeList) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

// 客户端请求消息
type ClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetIp   string `protobuf:"bytes,1,opt,name=target_ip,json=targetIp,proto3" json:"target_ip,omitempty"`       // 目标节点 IP
	TargetPort string `protobuf:"bytes,2,opt,name=target_port,json=targetPort,proto3" json:"target_port,omitempty"` // 目标节点端口
	Message    []byte `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`                         // 客户端要发送的消息
}

func (x *ClientRequest) Reset() {
	*x = ClientRequest{}
	mi := &file_connection_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientRequest) ProtoMessage() {}

func (x *ClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientRequest.ProtoReflect.Descriptor instead.
func (*ClientRequest) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{5}
}

func (x *ClientRequest) GetTargetIp() string {
	if x != nil {
		return x.TargetIp
	}
	return ""
}

func (x *ClientRequest) GetTargetPort() string {
	if x != nil {
		return x.TargetPort
	}
	return ""
}

func (x *ClientRequest) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

// 服务器响应
type ServerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"` // 服务器的响应消息
}

func (x *ServerResponse) Reset() {
	*x = ServerResponse{}
	mi := &file_connection_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerResponse) ProtoMessage() {}

func (x *ServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_connection_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerResponse.ProtoReflect.Descriptor instead.
func (*ServerResponse) Descriptor() ([]byte, []int) {
	return file_connection_proto_rawDescGZIP(), []int{6}
}

func (x *ServerResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_connection_proto protoreflect.FileDescriptor

var file_connection_proto_rawDesc = []byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x35, 0x0a, 0x0b, 0x50, 0x69,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x35, 0x0a, 0x0e, 0x50, 0x69,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x75, 0x0a, 0x14, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x70, 0x69, 0x63,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x20, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x67, 0x0a, 0x0d, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x2a, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32,
	0xcc, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x0d, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x10, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x15, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x16, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x04,
	0x5a, 0x02, 0x2e, 0x2f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_connection_proto_rawDescOnce sync.Once
	file_connection_proto_rawDescData = file_connection_proto_rawDesc
)

func file_connection_proto_rawDescGZIP() []byte {
	file_connection_proto_rawDescOnce.Do(func() {
		file_connection_proto_rawDescData = protoimpl.X.CompressGZIP(file_connection_proto_rawDescData)
	})
	return file_connection_proto_rawDescData
}

var file_connection_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_connection_proto_goTypes = []any{
	(*PictureData)(nil),          // 0: models.PictureData
	(*Empty)(nil),                // 1: models.Empty
	(*PictureRequest)(nil),       // 2: models.PictureRequest
	(*LocalPictureResponse)(nil), // 3: models.LocalPictureResponse
	(*NodeList)(nil),             // 4: models.NodeList
	(*ClientRequest)(nil),        // 5: models.ClientRequest
	(*ServerResponse)(nil),       // 6: models.ServerResponse
}
var file_connection_proto_depIdxs = []int32{
	0, // 0: models.LocalPictureResponse.picture_data:type_name -> models.PictureData
	1, // 1: models.ConnectionService.GetNodeTable:input_type -> models.Empty
	5, // 2: models.ConnectionService.SendMessage:input_type -> models.ClientRequest
	2, // 3: models.ConnectionService.SendLocalPicture:input_type -> models.PictureRequest
	4, // 4: models.ConnectionService.GetNodeTable:output_type -> models.NodeList
	6, // 5: models.ConnectionService.SendMessage:output_type -> models.ServerResponse
	3, // 6: models.ConnectionService.SendLocalPicture:output_type -> models.LocalPictureResponse
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_connection_proto_init() }
func file_connection_proto_init() {
	if File_connection_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_connection_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_connection_proto_goTypes,
		DependencyIndexes: file_connection_proto_depIdxs,
		MessageInfos:      file_connection_proto_msgTypes,
	}.Build()
	File_connection_proto = out.File
	file_connection_proto_rawDesc = nil
	file_connection_proto_goTypes = nil
	file_connection_proto_depIdxs = nil
}

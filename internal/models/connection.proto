syntax = "proto3";

package models;

option go_package="./";

// 定义服务
service ConnectionService {
  // 获取已知节点信息
  rpc GetNodeTable (Empty) returns (NodeList);
  // 客户端请求，选择目标节点并发送消息
  rpc SendMessage (ClientRequest) returns (ServerResponse);
  // 传输本地存储图片
  rpc SendLocalPicture(PictureRequest) returns (LocalPictureResponse);
}

message PictureData {
  string name = 1; // 图片名称
  bytes data = 2; // 图片字节数据
}

// 空的请求，表示无参数
message Empty {}

// 用于发送图片请求时的参数，可指定图片数量，默认全部
message PictureRequest {
  int32 picture_count = 1; // 要发送的图片数量，小于等于0表示发送全部图片
}

// 响应消息，包含图片数据（以字节流形式等）以及可能的相关响应信息
message LocalPictureResponse {
  repeated PictureData picture_data = 1; // 图片数据结构体列表，每张图片对应一个PictureData实例
  string status_message = 2; // 发送状态相关的消息，比如成功、失败原因等
}

// 节点信息列表
message NodeList {
  repeated string nodes = 1; // 存储节点信息的字符串列表
}

// 客户端请求消息
message ClientRequest {
  string target_ip = 1;  // 目标节点 IP
  string target_port = 2; // 目标节点端口
  bytes message = 3; // 客户端要发送的消息
}

// 服务器响应
message ServerResponse {
  string message = 1; // 服务器的响应消息
}

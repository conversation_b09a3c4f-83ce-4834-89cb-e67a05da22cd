// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.0
// source: connection.proto

package models

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ConnectionService_GetNodeTable_FullMethodName     = "/models.ConnectionService/GetNodeTable"
	ConnectionService_SendMessage_FullMethodName      = "/models.ConnectionService/SendMessage"
	ConnectionService_SendLocalPicture_FullMethodName = "/models.ConnectionService/SendLocalPicture"
)

// ConnectionServiceClient is the client API for ConnectionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 定义服务
type ConnectionServiceClient interface {
	// 获取已知节点信息
	GetNodeTable(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*NodeList, error)
	// 客户端请求，选择目标节点并发送消息
	SendMessage(ctx context.Context, in *ClientRequest, opts ...grpc.CallOption) (*ServerResponse, error)
	// 传输本地存储图片
	SendLocalPicture(ctx context.Context, in *PictureRequest, opts ...grpc.CallOption) (*LocalPictureResponse, error)
}

type connectionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConnectionServiceClient(cc grpc.ClientConnInterface) ConnectionServiceClient {
	return &connectionServiceClient{cc}
}

func (c *connectionServiceClient) GetNodeTable(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*NodeList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeList)
	err := c.cc.Invoke(ctx, ConnectionService_GetNodeTable_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectionServiceClient) SendMessage(ctx context.Context, in *ClientRequest, opts ...grpc.CallOption) (*ServerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ServerResponse)
	err := c.cc.Invoke(ctx, ConnectionService_SendMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectionServiceClient) SendLocalPicture(ctx context.Context, in *PictureRequest, opts ...grpc.CallOption) (*LocalPictureResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocalPictureResponse)
	err := c.cc.Invoke(ctx, ConnectionService_SendLocalPicture_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConnectionServiceServer is the server API for ConnectionService service.
// All implementations must embed UnimplementedConnectionServiceServer
// for forward compatibility.
//
// 定义服务
type ConnectionServiceServer interface {
	// 获取已知节点信息
	GetNodeTable(context.Context, *Empty) (*NodeList, error)
	// 客户端请求，选择目标节点并发送消息
	SendMessage(context.Context, *ClientRequest) (*ServerResponse, error)
	// 传输本地存储图片
	SendLocalPicture(context.Context, *PictureRequest) (*LocalPictureResponse, error)
	mustEmbedUnimplementedConnectionServiceServer()
}

// UnimplementedConnectionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedConnectionServiceServer struct{}

func (UnimplementedConnectionServiceServer) GetNodeTable(context.Context, *Empty) (*NodeList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeTable not implemented")
}
func (UnimplementedConnectionServiceServer) SendMessage(context.Context, *ClientRequest) (*ServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedConnectionServiceServer) SendLocalPicture(context.Context, *PictureRequest) (*LocalPictureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendLocalPicture not implemented")
}
func (UnimplementedConnectionServiceServer) mustEmbedUnimplementedConnectionServiceServer() {}
func (UnimplementedConnectionServiceServer) testEmbeddedByValue()                           {}

// UnsafeConnectionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConnectionServiceServer will
// result in compilation errors.
type UnsafeConnectionServiceServer interface {
	mustEmbedUnimplementedConnectionServiceServer()
}

func RegisterConnectionServiceServer(s grpc.ServiceRegistrar, srv ConnectionServiceServer) {
	// If the following call pancis, it indicates UnimplementedConnectionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ConnectionService_ServiceDesc, srv)
}

func _ConnectionService_GetNodeTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectionServiceServer).GetNodeTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectionService_GetNodeTable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectionServiceServer).GetNodeTable(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectionService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectionServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectionService_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectionServiceServer).SendMessage(ctx, req.(*ClientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectionService_SendLocalPicture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PictureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectionServiceServer).SendLocalPicture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectionService_SendLocalPicture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectionServiceServer).SendLocalPicture(ctx, req.(*PictureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ConnectionService_ServiceDesc is the grpc.ServiceDesc for ConnectionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConnectionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "models.ConnectionService",
	HandlerType: (*ConnectionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNodeTable",
			Handler:    _ConnectionService_GetNodeTable_Handler,
		},
		{
			MethodName: "SendMessage",
			Handler:    _ConnectionService_SendMessage_Handler,
		},
		{
			MethodName: "SendLocalPicture",
			Handler:    _ConnectionService_SendLocalPicture_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "connection.proto",
}

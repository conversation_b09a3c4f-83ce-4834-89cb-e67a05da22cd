// 两个table的交集

package discovery

func FindUnion(udp_table, arp_table []RemoteTable) []RemoteTable {
	// 使用 map 存储地址对应的 RemoteTable
	unionMap := make(map[string]RemoteTable)

	// 遍历 udp_table 并加入 unionMap
	for _, udpEntry := range udp_table {
		if existing, exists := unionMap[udpEntry.addr]; exists {
			// 如果 addr 已存在，合并 PortStyle 列表
			unionMap[udpEntry.addr] = RemoteTable{
				addr: udpEntry.addr,
				ps:   mergePortStyles(existing.ps, udpEntry.ps),
			}
		} else {
			// 如果 addr 不存在，直接添加
			unionMap[udpEntry.addr] = udpEntry
		}
	}

	// 遍历 arp_table 并加入 unionMap
	for _, arpEntry := range arp_table {
		if existing, exists := unionMap[arpEntry.addr]; exists {
			// 如果 addr 已存在，合并 PortStyle 列表
			unionMap[arpEntry.addr] = RemoteTable{
				addr: arpEntry.addr,
				ps:   mergePortStyles(existing.ps, arpEntry.ps),
			}
		} else {
			// 如果 addr 不存在，直接添加
			unionMap[arpEntry.addr] = arpEntry
		}
	}

	// 将 unionMap 中的 RemoteTable 转为切片并返回
	var union []RemoteTable
	for _, entry := range unionMap {
		union = append(union, entry)
	}

	return union
}
func mergePortStyles(a, b []PortStyle) []PortStyle {
	// 使用 map 去除重复的 PortStyle
	mergedMap := make(map[string]PortStyle)
	for _, p := range a {
		mergedMap[p.port+p.pf] = p
	}
	for _, p := range b {
		mergedMap[p.port+p.pf] = p
	}

	// 将结果从 map 中转回切片
	var merged []PortStyle
	for _, p := range mergedMap {
		merged = append(merged, p)
	}

	return merged
}

// 比较两个 PortStyle 切片是否相等
func portStyleEqual(a, b []PortStyle) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i].port != b[i].port || a[i].pf != b[i].pf {
			return false
		}
	}
	return true
}

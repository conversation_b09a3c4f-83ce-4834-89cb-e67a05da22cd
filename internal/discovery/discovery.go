package discovery

import (
	"bufio"
	"fmt"
	"log"
	"net"
	"os/exec"
	"strings"
	"time"
)

var Local_address string // 本机的内网地址

const broadcastAddress = "***************:50052" // 广播地址，广播端口 (UDP)

// NodeTable 存储已知节点的信息
// var NodeTable sync.Map

type PortStyle struct {
	port string
	pf   string
}

type RemoteTable struct {
	addr string
	ps   []PortStyle
}

var toping_ps = []PortStyle{
	//{"21", "ftp"},
	{"22", "sftp、ssh"},
	{"23", "telnet"},
	//{"25", "smtp"},
	//{"80", "http"},
	//{"443", "https"},
}

var udp_table []RemoteTable
var arp_table []RemoteTable
var final_table []RemoteTable

// UpdateArpTable 更新ARP发现表
func UpdateArpTable() {
	arp_table = make([]RemoteTable, 0)

	// 调用系统的 arp 命令
	cmd := exec.Command("arp", "-n")
	output, err := cmd.Output()
	if err != nil {
		log.Fatalf("failed to run arp command: %v", err)
	}
	// 使用 bufio.Scanner 逐行读取输出
	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	// 跳过第一行（列标题）
	scanner.Scan()
	// 遍历 ARP 表的每一行，提取 IP 地址
	for scanner.Scan() {
		line := scanner.Text()
		// 按空格分割每行
		fields := strings.Fields(line)
		if len(fields) > 0 {
			// 将第一列的 IP 地址存入数组
			r := RemoteTable{
				addr: fields[0],
			}
			arp_table = append(arp_table, r)
		}
	}
	//log.Println("ARP_table: ", arp_table)
}

// UpdateIntranetIP 获取本机第一个非环回地址，更新至全局
func UpdateIntranetIP() {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		log.Println(err)
		return
	}

	for _, addr := range addrs {
		// 检查地址是否是 IP 地址，并且不是环回地址
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				Local_address = ipNet.IP.To4().String()
				return
			}
		}
	}
}

// 启动 UDP 监听服务，接收来自其他节点的ip发现广播
func StartUDPServer(port string) {
	// 启动 UDP 服务监听
	udp_table = make([]RemoteTable, 0)
	addr := fmt.Sprintf(":%s", port)
	conn, err := net.ListenPacket("udp", addr)
	if err != nil {
		log.Fatalf("Failed to listen on UDP port %s: %v", port, err)
	}
	log.Println("UDP Listening on", ":"+port)
	defer conn.Close()

	buffer := make([]byte, 1024)
	for {
		// 接收节点发送的服务发现请求
		_, addr, err := conn.ReadFrom(buffer)
		if err != nil {
			log.Printf("Failed to read from UDP connection: %v", err)
			continue
		}
		//log.Println(addr.String())

		fromip, _, _ := net.SplitHostPort(addr.String())
		exists := false
		for _, entry := range udp_table {
			if entry.addr == fromip {
				exists = true
				break
			}
		}

		if !exists { // 如果 fromip 不存在，则插入到 udp_table 中
			udp_table = append(udp_table, RemoteTable{addr: fromip})
			//log.Printf("Inserted new entry for IP: %s", fromip)
		}

		// to-do
		// udp表会不断扩展，维护udp表 + 清理udp表

		//log.Println("UDP_table:", udp_table)
	}
}

// 启动TCP监听器服务。通过验证才会正常进行握手
func StartTCPServer(address, port string) {
	listener, err := net.Listen("tcp", address+":"+port)
	if err != nil {
		log.Printf("error starting TCP server: %v", err)
	}
	log.Println("TCP Listening on", address+":"+port)
	defer listener.Close()
	// 持续接受连接并处理每个连接
	for {
		conn, err := listener.Accept() // 接受客户端连接
		if err != nil {
			log.Printf("Error accepting TCP connection: %v\n", err)
			continue
		}

		// 处理客户端连接
		//log.Printf("TCP connect from: %s\n", conn.RemoteAddr())

		// 进行连接验证（握手）
		if !AuthenticateClient(conn) {
			conn.Close() // 如果验证失败，关闭连接
			continue
		}

		// 读取客户端消息并回传响应
		HandleTCPConnection(conn)
	}
}

func UpdateFinalTable() {
	final_table = []RemoteTable{}            // 清理
	union := FindUnion(udp_table, arp_table) // 计算并集
	for _, v := range union {                //遍历udp和arp的发现表
		if SendTCPServer(v.addr, "50054", "hi") { //确定是自己的机器
			ps := make([]PortStyle, 0)
			for _, p := range toping_ps { //确定端口
				if Ping_port(v.addr, p.port) {
					ps = append(ps, p)
				}
			}
			final_table = append(final_table, RemoteTable{v.addr, ps})
		}
	}
	log.Printf("UpdateFinalTable(): final_table[%d] \n %v", len(final_table), final_table)
}

func StartDiscover() {
	for {
		UpdateIntranetIP()

		SendUDPBroadcast("127.0.0.1:50052")

		UpdateArpTable() // 更新ARP表

		UpdateFinalTable() // 根据广播发现和ARP发现进行TCP握手，并更新至全局变量

		time.Sleep(60 * time.Second)
	}
}

// 获取所有已知节点的信息
func GetNodeTable() []string {
	var nodes []string
	//fmt.Println(final_table)
	for _, entry := range final_table {
		var ports []string
		for _, port := range entry.ps {
			ports = append(ports, fmt.Sprintf("%s (%s)", port.port, port.pf))
		}
		nodeInfo := fmt.Sprintf("%s -> [%s]", entry.addr, strings.Join(ports, ", "))
		nodes = append(nodes, nodeInfo)
	}
	return nodes
}

// TCP连接发现和UDP广播发送
package discovery

import (
	"bufio"
	"fmt"
	"log"
	"net"
	"strings"
	"time"
)

var validKey = "validatedTCP"

// 验证客户端是否通过握手
func AuthenticateClient(conn net.Conn) bool {
	// 创建读写缓冲区
	reader := bufio.NewReader(conn)
	writer := bufio.NewWriter(conn)

	// 发送握手请求
	writer.WriteString("Please send the handshake key: ")
	writer.Flush()

	// 读取客户端发送的握手密钥
	key, err := reader.ReadString('\n')
	if err != nil {
		log.Println("Error reading handshake key:", err)
		return false
	}

	// 去除末尾的换行符
	key = strings.TrimSpace(key)

	// 验证握手密钥是否正确
	if key == validKey {
		writer.WriteString("Authentication successful!\n")
		writer.Flush()
		return true
	}

	// 握手失败，关闭连接
	writer.WriteString("Authentication failed. Closing connection.\n")
	writer.Flush()
	return false
}

// 处理客户端TCP连接的函数
func HandleTCPConnection(conn net.Conn) {
	defer conn.Close() // 确保连接关闭

	// 创建读写缓冲区
	reader := bufio.NewReader(conn)
	writer := bufio.NewWriter(conn)

	// 持续接收客户端消息并回复
	for {
		// 读取客户端发送的消息
		message, err := reader.ReadString('\n')
		if err != nil {
			//log.Printf("TCP Connection closed by client %s: %v\n", conn.RemoteAddr(), err)
			break
		}
		//log.Printf("TCP Message from %s: %s", conn.RemoteAddr(), message)

		// 回应客户端
		response := fmt.Sprintf("TCP Server received: %s", message)
		writer.WriteString(response)
		writer.Flush()
	}
}

func SendTCPServer(address, port, message string) bool {
	conn, err := net.DialTimeout("tcp", address+":"+port, 1*time.Second) // 连接到服务器
	if err != nil {
		//log.Printf("%v\n", err)
		return false
	}
	defer conn.Close()

	// 创建读写缓冲区
	writer := bufio.NewWriter(conn)
	reader := bufio.NewReader(conn)

	// 发送握手密钥
	writer.WriteString(validKey + "\n") // 发送正确的密钥
	writer.Flush()

	// 读取服务器返回的握手响应
	response, err := reader.ReadString('\n')
	if err != nil {
		log.Printf("failed to read handshake response: %v", err)
		return false
	}

	if !strings.Contains(response, "Authentication successful") {
		log.Printf("authentication failed: %s\n", response)
		return false
	}

	//log.Println("TCP sending to ", address+":"+port) // 发送消息
	_, err = writer.WriteString(message + "\n")
	if err != nil {
		log.Printf("failed to write message: %v", err)
		return false
	}
	writer.Flush()

	// 读取服务器响应
	_, err = reader.ReadString('\n')
	if err != nil {
		log.Printf("failed to read response: %v", err)
		return false
	}
	//log.Printf("Received server response: %s", serverResponse)

	return true
}

// Ping_port 在TCP连接确认是所属主机后，继续连通该ip的端口，返回true或false
func Ping_port(addr, port string) bool {
	address := fmt.Sprintf("%s:%s", addr, port)
	conn, err := net.DialTimeout("tcp", address, 1*time.Second)
	if err != nil {
		return false
	}
	defer conn.Close() // 连接成功后关闭连接
	return true        // 如果没有出错，返回 true
}

// 启动定时 UDP 广播，主动将自己节点的信息广播给局域网中的其他节点。目的是暴露自己ip
func SendUDPBroadcast(to_broadcast string) {
	// 通过 UDP 广播发送节点信息
	addr, err := net.ResolveUDPAddr("udp", broadcastAddress)
	if err != nil {
		log.Fatalf("Failed to resolve UDP address: %v", err)
	}
	//log.Println("Resolved UDP addr:", addr)
	conn, err := net.DialUDP("udp", nil, addr)
	if err != nil {
		log.Fatalf("Failed to send UDP broadcast: %v", err)
	}
	defer conn.Close()

	// 向目标广播地址发送节点信息（例如：IP:Port）
	_, err = conn.Write([]byte(to_broadcast))
	if err != nil {
		log.Printf("Failed to broadcast node info: %v", err)
	}
	//log.Printf("Broadcasted info: %s", to_broadcast)
}

package server

import (
	"FBgo/internal/clients"
	"FBgo/internal/discovery"
	"FBgo/internal/models"

	"context"
	"encoding/binary"
	"fmt"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"io"
	"log"
	"net"
	"os"
	"path/filepath"
	"sort"
	"time"
)

const (
	serverListenGrpcAddr = ":50051"
	serverListenTcpAddr  = ":50053"
)

// gRPC 服务实现结构体
type server struct {
	models.UnimplementedConnectionServiceServer
}

// 日志记录器
var logger = logrus.New()

// GetNodeTable 返回当前已知的所有节点信息
func (s *server) GetNodeTable(ctx context.Context, req *models.Empty) (*models.NodeList, error) {
	nodes := discovery.GetNodeTable()
	nodeList := &models.NodeList{}
	for _, node := range nodes {
		nodeList.Nodes = append(nodeList.Nodes, node)
	}
	logger.Infof("Fetched node table with %d nodes", len(nodes))
	return nodeList, nil
}

func handleClientRequest(conn net.Conn) {
	defer func(conn net.Conn) {
		err := conn.Close()
		if err != nil {
			log.Printf("Failed to close connection: %v", err)
		}
	}(conn)
	semaphore := make(chan struct{}, 10)
	semaphore <- struct{}{}
	defer func() { <-semaphore }()
	err := conn.SetDeadline(time.Now().Add(5 * time.Second))
	if err != nil {
		log.Printf("Failed to set deadline: %v", err)
		return
	}
	var messageLength int32
	if err := binary.Read(conn, binary.BigEndian, &messageLength); err != nil {
		log.Printf("Failed to read message length: %v", err)
		return
	}
	message := make([]byte, messageLength)
	if _, err := io.ReadFull(conn, message); err != nil {
		log.Printf("Failed to read full message: %v", err)
		return
	}
	imageName, imageData, err := parseImageInfo(message)
	if err != nil {
		log.Printf("Failed to parse image info: %v", err)
		response := fmt.Sprintf("Failed to parse image info: %v", err)
		if err := sendResponse(conn, response); err != nil {
			log.Printf("Failed to send error response: %v", err)
		}
		return
	}
	imagePath := filepath.Join("./Picture", imageName)
	if err := saveImageToFile(imagePath, imageData); err != nil {
		log.Printf("Failed to save image: %v", err)
		response := fmt.Sprintf("Failed to save image: %v", err)
		if err := sendResponse(conn, response); err != nil {
			log.Printf("Failed to send error response: %v", err)
		}
		return
	}
	log.Printf("Image saved successfully to %s", imageName)
	response := fmt.Sprintf("Image saved successfully to %s", imageName)
	if err := sendResponse(conn, response); err != nil {
		log.Printf("Failed to send response: %v", err)
	}
}

// parseImageInfo 从接收到的字节数据中解析出图片名和图片数据
func parseImageInfo(data []byte) (string, []byte, error) {
	if len(data) == 0 {
		return "", nil, fmt.Errorf("empty message data")
	}
	nameLength := int(data[0])
	if nameLength < 0 || nameLength >= len(data)-1 {
		return "", nil, fmt.Errorf("invalid image name length")
	}
	imageName := string(data[1 : 1+nameLength])
	imageData := data[1+nameLength:]
	return imageName, imageData, nil
}

// saveImageToFile 将收到的字节数组保存为文件
func saveImageToFile(filePath string, data []byte) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer func(file *os.File) {
		if err := file.Close(); err != nil {
			log.Printf("Failed to close file: %v", err)
		}
	}(file)
	if _, err := file.Write(data); err != nil {
		return fmt.Errorf("failed to write data to file: %v", err)
	}
	fmt.Println("File saved successfully")
	return nil
}

// sendResponse 发送响应到源机器上
func sendResponse(conn net.Conn, response string) error {
	if err := binary.Write(conn, binary.BigEndian, int32(len(response))); err != nil {
		logger.Errorf("Failed to write response length: %v", err)
		return fmt.Errorf("failed to write response length: %v", err)
	}
	if _, err := conn.Write([]byte(response)); err != nil {
		logger.Errorf("Failed to send response: %v", err)
		return fmt.Errorf("failed to send response: %v", err)
	}
	return nil
}

// SendMessage 向目标节点发送消息并返回响应
func (s *server) SendMessage(ctx context.Context, req *models.ClientRequest) (*models.ServerResponse, error) {
	targetIP := req.GetTargetIp()
	targetPort := req.GetTargetPort()
	message := req.GetMessage()
	response, err := clients.SendMessage(targetIP, targetPort, message)
	if err != nil {
		logger.Errorf("Error sending message: %v", err)
		return &models.ServerResponse{Message: "Error sending message"}, err
	}

	logger.Infof("Message sent to %s:%s, response: %s", targetIP, targetPort, response)
	return &models.ServerResponse{
		Message: response,
	}, nil
}

// StartServer 启动gRPC服务器和TCP服务器并监听来自客户端的请求
func StartServer() {
	grpcListener, err := net.Listen("tcp", serverListenGrpcAddr)
	if err != nil {
		logger.Fatalf("Failed to listen on TCP port 50051: %v", err)
	}
	defer func(grpcListener net.Listener) {
		err := grpcListener.Close()
		if err != nil {
			logger.Errorf("Failed to close listener: %v", err)
		}
	}(grpcListener)
	grpcServer := grpc.NewServer()
	models.RegisterConnectionServiceServer(grpcServer, &server{})
	logger.Infof("Starting gRPC server on port %s", serverListenGrpcAddr)
	go func() {
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.Fatalf("Failed to serve gRPC server: %v", err)
		}
	}()
	tcpListener, err := net.Listen("tcp", ":50053")
	logger.Infof("Starting gRPC TCP server on port %s", serverListenTcpAddr)
	if err != nil {
		logger.Fatalf("Failed to listen on TCP port 50052: %v", err)
	}
	defer func(tcpListener net.Listener) {
		err := tcpListener.Close()
		if err != nil {
		}
	}(tcpListener)
	for {
		conn, err := tcpListener.Accept()
		if err != nil {
			logger.Errorf("Failed to accept connection: %v", err)
			continue
		}
		go handleClientRequest(conn)
	}
}

func (s *server) SendLocalPicture(ctx context.Context, req *models.PictureRequest) (*models.LocalPictureResponse, error) {
	var picturePaths []string
	var sendPicturePaths []string
	var pictureDataList []*models.PictureData
	pictureDir := "./Picture"
	pictureFiles, err := os.ReadDir(pictureDir)
	if err != nil {
		return &models.LocalPictureResponse{
			StatusMessage: fmt.Sprintf("Failed to read picture directory: %v", err),
		}, err
	}
	for _, file := range pictureFiles {
		if file.IsDir() {
			continue
		}
		picturePaths = append(picturePaths, filepath.Join(pictureDir, file.Name()))
	}
	sort.Strings(picturePaths)
	if req.PictureCount <= 0 || req.PictureCount > int32(len(picturePaths)) {
		sendPicturePaths = picturePaths
	} else {
		sendPicturePaths = picturePaths[:req.PictureCount]
	}
	for _, path := range sendPicturePaths {
		fileData, readErr := os.ReadFile(path)
		if readErr != nil {
			return &models.LocalPictureResponse{
				StatusMessage: fmt.Sprintf("Failed to read picture file: %v", readErr),
			}, readErr
		}
		imageName := filepath.Base(path)
		pictureDataList = append(pictureDataList, &models.PictureData{
			Name: imageName,
			Data: fileData,
		})
	}
	return &models.LocalPictureResponse{
		PictureData:   pictureDataList,
		StatusMessage: "Picture sent successfully",
	}, nil
}

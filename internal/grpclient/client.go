package main

import (
	"FBgo/internal/models"
	"context"
	"fmt"
	"google.golang.org/grpc"
	"log"
	"os"
	"path/filepath"
	"time"
)

const (
	grpcServerAddress = "************:50051"
	defaultTargetIP   = "************"
	defaultTargetPort = "50053"
)

func main() {
	conn, err := grpc.Dial(grpcServerAddress, grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to gRPC server: %v", err)
	}
	defer func(conn *grpc.ClientConn) {
		if err := conn.Close(); err != nil {
			log.Fatalf("Failed to close connection: %v", err)
		}
	}(conn)
	client := models.NewConnectionServiceClient(conn)
	testGetNodeTable(client)
	testSendMessage(client)
	_, err = testSendLocalPicture(client)
	if err != nil {
		return
	}
}

func testGetNodeTable(client models.ConnectionServiceClient) {
	fmt.Println("Testing GetNodeTable...")
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	response, err := client.GetNodeTable(ctx, &models.Empty{})
	if err != nil {
		log.Fatalf("Error calling GetNodeTable: %v", err)
	}
	fmt.Printf("Node table fetched successfully. Nodes: %v\n", response.GetNodes())
}

// 发送消息，允许用户输入目标 IP 和端口
func testSendMessage(client models.ConnectionServiceClient) {
	fmt.Println("Testing SendMessage...")
	var targetIP, targetPort string
	fmt.Print("Enter the target IP (leave blank for default): ")
	fmt.Scanln(&targetIP)
	if targetIP == "" {
		targetIP = defaultTargetIP
	}
	fmt.Print("Enter the target Port (leave blank for default): ")
	fmt.Scanln(&targetPort)
	if targetPort == "" {
		targetPort = defaultTargetPort
	}
	fmt.Printf("Using target IP: %s, Port: %s\n", targetIP, targetPort)
	testSendMessageWithParams(client, targetIP, targetPort)
}

// 发送消息的实际逻辑
func testSendMessageWithParams(client models.ConnectionServiceClient, targetIP, targetPort string) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	pictureDir := filepath.Join(".", "picture")
	files, err := os.ReadDir(pictureDir)
	if err != nil {
		log.Fatalf("Error reading picture folder: %v", err)
	}
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		imagePath := filepath.Join(pictureDir, file.Name())
		imageData, err := os.ReadFile(imagePath)
		if err != nil {
			log.Fatalf("Error reading image file: %v", err)
		}
		imageName := file.Name()
		message := make([]byte, 1+len(imageName)+len(imageData))
		message[0] = byte(len(imageName))
		copy(message[1:1+len(imageName)], []byte(imageName))
		copy(message[1+len(imageName):], imageData)
		req := &models.ClientRequest{
			TargetIp:   targetIP,
			TargetPort: targetPort,
			Message:    message,
		}
		t := time.Now()
		response, err := client.SendMessage(ctx, req)
		elapsed := time.Since(t).Milliseconds()
		fmt.Printf("Time taken: %d ms\n", elapsed)
		if err != nil {
			log.Fatalf("Error calling SendMessage: %v", err)
		}
		fmt.Printf("Response from SendMessage: %s\n", response.GetMessage())
	}
}

func testSendLocalPicture(client models.ConnectionServiceClient) (bool, error) {
	req := &models.PictureRequest{
		PictureCount: 0,
	}
	resp, err := client.SendLocalPicture(context.Background(), req)
	if err != nil {
		return false, fmt.Errorf("Could not send local picture: %v", err)
	}
	fmt.Printf("Received %d pictures\n", len(resp.PictureData))

	if resp.StatusMessage == "Picture sent successfully" {
		for _, picture := range resp.PictureData {
			imagePath := filepath.Join("./pictures", picture.Name)
			if err := saveImageToFile(imagePath, picture.Data); err != nil {
				log.Printf("Failed to save image: %v", err)
			} else {
				log.Printf("Image saved successfully to %s", imagePath)
			}
		}
		return true, nil
	}
	return false, fmt.Errorf("Error sending picture: %s", resp.StatusMessage)
}

// saveImageToFile 将收到的字节数组保存为文件
func saveImageToFile(filePath string, data []byte) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("Failed to create file: %v", err)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			log.Printf("Failed to close file: %v", err)
		}
	}(file)

	if _, err := file.Write(data); err != nil {
		return fmt.Errorf("Failed to write data to file: %v", err)
	}
	fmt.Println("File saved successfully")
	return nil
}
